<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\B2CAgencyConfig;
use App\Models\ContentSources;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class B2CAgencyConfigController extends Controller
{
  public function index(Request $request, $id)
  {
    $b2cConfig = B2CAgencyConfig::where('group_id', $id)->first();

    $content_sources = ContentSources::where('status', 1)->get(['id','identifier', 'name'])->toArray();

    return view('backend.agencygroup.b2cagencyconfig.form', compact(['id', 'b2cConfig', 'content_sources']));
  }
  public function store(Request $request)
{
   
}
public function update(Request $request)
{
    
}


}
