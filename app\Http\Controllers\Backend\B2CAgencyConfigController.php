<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\B2CAgencyConfig;
use App\Models\ContentSources;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class B2CAgencyConfigController extends Controller
{
  public function index(Request $request, $id)
  {
    $b2cConfig = B2CAgencyConfig::where('group_id', $id)->first();

    $content_sources = ContentSources::where('status', 1)->get(['id','identifier', 'name'])->toArray();

    return view('backend.agencygroup.b2cagencyconfig.form', compact(['id', 'b2cConfig', 'content_sources']));
  }
  public function store(Request $request)
{
    $validated = $request->validate([
        'group_id'       => 'required|string',
        'market_country' => 'required|string',
        'currency'       => 'required|string', 
        'status'         => 'nullable|boolean',
        'b2c_config'     => 'required|array',
        'meta_config'    => 'nullable|array',
        'b2b_config'     => 'nullable|array',
    ]);

    $data = [
        'group_id'       => $validated['group_id'],
        'market_country' => $validated['market_country'],
        'currency'       => $validated['currency'],
        'is_status'      => $validated['status'] ?? false,
        'b2c_config'     => $validated['b2c_config'],
        'meta_config'    => $validated['meta_config'] ?? [],
        'b2b_config'     => $validated['b2b_config'] ?? [],
    ];

    $config = B2CAgencyConfig::updateOrCreate(
        ['group_id' => $validated['group_id']],
        $data
    );

    return redirect()->back()->with('success', $config->wasRecentlyCreated 
        ? 'Configuration saved successfully' 
        : 'Configuration updated successfully');
}
public function update(Request $request)
{
    $validator = Validator::make($request->all(), [
        'group_id'            => 'required|string',
        'edit_market_country' => 'required|string',
        'edit_currency'       => 'required|string',
        'status'              => 'nullable|boolean',
        'b2c_config'          => 'required|array',
        'meta_config'         => 'nullable|array',
        'b2b_config'          => 'nullable|array',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'status'  => 'error',
            'errors'  => $validator->errors()
        ], 422);
    }

    $validated = $validator->validated();

    $config = B2CAgencyConfig::where('group_id', $validated['group_id'])->firstOrFail();

    $data = [
        'market_country' => $validated['edit_market_country'],
        'currency'       => $validated['edit_currency'],
        'is_status'      => $validated['status'] ?? false,
        'b2c_config'     => $validated['b2c_config'],
        'meta_config'    => $validated['meta_config'] ?? [],
        'b2b_config'     => $validated['b2b_config'] ?? [],
    ];

    $config->update($data);

    return response()->json([
        'status'  => 'success',
        'message' => 'Configuration updated successfully'
    ]);
}


}
