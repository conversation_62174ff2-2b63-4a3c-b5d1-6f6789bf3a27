<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('b2c_agency_configs', function (Blueprint $table) {
            $table->id();

            $table->foreignId('agency_profile_id')
                  ->constrained('agency_profiles')
                  ->onDelete('cascade');

            $table->string('domain');
            $table->string('site_name');
            $table->string('platform_name');
            $table->string('site_email');
            $table->string('market_type');
            $table->string('site_phone');
            $table->string('portal_id')->unique();  // Added unique() here

            $table->string('rsource'); 
            $table->json('api_key')->nullable();
            $table->json('client_id')->nullable(); 

            $table->string('content_sources');
            $table->string('landing_url');

            $table->boolean('booking_mode')->default(false);
            $table->boolean('active')->default(true);

            $table->timestamps();      
            $table->softDeletes();    
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('b2c_agency_configs');
    }
};