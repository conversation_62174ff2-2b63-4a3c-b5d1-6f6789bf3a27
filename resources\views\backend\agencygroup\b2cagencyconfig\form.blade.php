@extends('layouts/layoutMaster')
@section('title', 'Agencies')
@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/katex.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/editor.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('assets/vendor/js/dropdown-hover.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/katex.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/quill.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
@endsection
@section('page-script')
    <script src="{{ asset('assets/js/agencygroup/index.js') }}"></script>
@endsection
@section('content')
    <style>
        .b2c_config_field {
            position: relative;
            margin-bottom: 30px;
        }

        .b2c_config_field::before {
            position: absolute;
            content: "Configuration";
            top: -12px;
            background-color: white;
            padding: 2px;
            font-weight: bold
        }
        
        .config-card {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            position: relative;
        }
        
        .remove-block {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        
        .common-fields {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .select2-container {
            width: 100% !important;
        }
    </style>

    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex ">
                <h5 class="mx-2 mt-2 text-white">B2C Config</h5>
            </div>
        </div>
    </div>
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show mt-2" role="alert" id="success-alert">
            {{ session('success') }}
        </div>
    @endif

    <div class="card p-3 mt-2">
        <div class="row">
            <div class="col-md-12">
                <form action="{{ route('backend.b2c-agency-config.store') }}" method="POST" id="b2cForm">
                    @csrf
                    <input type="hidden" name="group_id" value="{{ $id }}">
                    
                    <!-- Common Fields Section -->
                    <div class="common-fields">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <label for="market_country" class="form-label">Market Country</label>
                                @if (is_object($b2cConfig) && $b2cConfig->market_country)
                                    <select name="edit_market_country" class="selectpicker w-100" data-style="btn-default">
                                        <option value="US"{{ ($b2cConfig->market_country ?? '') == 'US' ? 'selected' : '' }}>US</option>
                                        <option value="CA"{{ ($b2cConfig->market_country ?? '') == 'CA' ? 'selected' : '' }}>CA</option>
                                    </select>
                                @else
                                    <select name="market_country" class="selectpicker w-100" data-style="btn-default">
                                        <option value="US">US</option>
                                        <option value="CA">CA</option>
                                    </select>
                                @endif
                            </div>
                            <div class="col-md-4 mb-4">
                                <label for="currency" class="form-label">Currency</label>
                                @if (is_object($b2cConfig) && $b2cConfig->currency)
                                    <select name="edit_currency" class="selectpicker w-100" data-style="btn-default">
                                        <option value="CAD" {{ ($b2cConfig->currency ?? '') == 'CAD' ? 'selected' : '' }}>CAD</option>
                                        <option value="USD" {{ ($b2cConfig->currency ?? '') == 'USD' ? 'selected' : '' }}>USD</option>
                                    </select>
                                @else
                                    <select name="currency" class="selectpicker w-100" data-style="btn-default">
                                        <option value="CAD">CAD</option>
                                        <option value="USD">USD</option>
                                    </select>
                                @endif
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label">Status</label>
                                <div class="form-check">
                                    @if (is_object($b2cConfig) && $b2cConfig->is_status)
                                        <input type="hidden" name="edit_status" value="0" />
                                        <input class="form-check-input" type="checkbox" name="edit_status" value="1"
                                            {{ old('status', $b2cConfig->is_status ?? 0) == 1 ? 'checked' : '' }} />
                                    @else
                                        <input type="hidden" name="status" value="0" />
                                        <input class="form-check-input" type="checkbox" name="status" value="1" />
                                    @endif
                                    <label class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <fieldset class="p-3 border rounded b2c_config_field">
                        <div id="b2c-container">
                                <div class="config-card b2c-block">
                                    <div class="row">
                                        <div class="col-md-3 mb-4">
                                            <label for="domain" class="form-label">Domain</label>
                                            <input type="text" class="form-control" name="b2c_config[0][domain]" />
                                        </div>
                                        <div class="col-md-3 mb-4">
                                            <label for="site_name" class="form-label">Site Name</label>
                                            <input type="text" class="form-control" name="b2c_config[0][site_name]" />
                                        </div>
                                        <div class="col-md-3 mb-4">
                                            <label for="platform_name" class="form-label">Platform Name</label>
                                            <input type="text" class="form-control" name="b2c_config[0][platform_name]" />
                                        </div>
                                        <div class="col-md-3 mb-4">
                                            <label for="site_email" class="form-label">Site Email</label>
                                            <input type="email" class="form-control" name="b2c_config[0][site_email]" />
                                        </div>
                                        <div class="col-md-3 mb-4">
                                            <label for="site_phone" class="form-label">Site Phone</label>
                                            <input type="text" class="form-control" name="b2c_config[0][site_phone]" />
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <label class="form-label">Rsource</label>
                                            <input type="text" class="form-control" name="b2c_config[0][rsource]" />
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <label class="form-label">Content Sources</label>
                                            <select class="form-control select2-content-sources" multiple="multiple"
                                                name="b2c_config[0][content_sources][]">
                                                @foreach($content_sources as $source)
                                                    <option value="{{ $source['id'] }}">
                                                        {{ $source['name'] }} ({{ $source['identifier'] }})
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">API Key</label>
                                            <input type="text" class="form-control" name="b2c_config[0][api_key]" />
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">Landing Url</label>
                                            <input type="text" class="form-control"
                                                name="b2c_config[0][landing_url]" />
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <label class="form-label">Booking Mode</label>
                                            <div class="form-check">
                                                <input type="hidden" name="b2c_config[0][gds_book]"
                                                    value="0" />
                                                <input class="form-check-input" type="checkbox"
                                                    name="b2c_config[0][gds_book]" value="1" />
                                                <label class="form-check-label">GDS Book</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <label class="form-label">Active</label>
                                            <div class="form-check">
                                                <input type="hidden" name="b2c_config[0][active]" value="0" />
                                                <input class="form-check-input" type="checkbox"
                                                    name="b2c_config[0][active]" value="1" />
                                                <label class="form-check-label">Active</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </fieldset>

                    <div class="row mt-4">
                        <div class="col-md-2">
                            @if ($b2cConfig)
                                <button type="submit" class="btn btn-success update" id="update">Update</button>
                            @else
                                <button type="submit" class="btn btn-success">Submit</button>
                            @endif
                        </div>
                        <div class="col-md-6">
                            @if ($errors->any())
                                <div class="alert alert-danger" id="error-alert">
                                    <ul class="mb-0" style="list-style: none">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <div id="error-message"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Initialize Select2 for content sources
            $('.select2-content-sources').select2({
                placeholder: "Select content sources",
                allowClear: true,
                width: '100%'
            });

            // Generate random API key
            function generateApiKey(length = 40) {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                let apiKey = '';
                for (let i = 0; i < length; i++) {
                    apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return apiKey;
            }
        });
    </script>
@endsection