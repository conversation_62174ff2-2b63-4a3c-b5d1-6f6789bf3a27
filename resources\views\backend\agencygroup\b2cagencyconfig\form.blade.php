@extends('layouts/layoutMaster')
@section('title', 'B2C Config')
@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/katex.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/editor.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('assets/vendor/js/dropdown-hover.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/katex.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/quill.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/agencygroup/index.js') }}"></script>
@endsection

@section('content')
    <style>
        .b2c_config_field {
            position: relative;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .b2c_config_field::before {
            position: absolute;
            content: "Configuration";
            top: -12px;
            left: 20px;
            background-color: white;
            padding: 0 10px;
            font-weight: bold;
            color: #333;
        }
        
        .config-card {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            position: relative;
            background-color: #f9f9f9;
        }
        

        
        .common-fields {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #eee;
        }
        
        .select2-container {
            width: 100% !important;
        }
        
        .header {
            background-color: #7367f0;
        }
        
        .header h5 {
            color: white;
        }
        
        #b2c-container {
            margin-top: 20px;
        }
        

        
        .api-client-card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            position: relative;
        }
        
        .remove-api-client {
            position: absolute;
            top: 5px;
            right: 5px;
            color: #dc3545;
            cursor: pointer;
        }
    </style>

    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex">
                <h5 class="mx-2 mt-2 text-white">B2C Config</h5>
            </div>
        </div>
    </div>
    
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show mt-2" role="alert" id="success-alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Configuration Details Table -->
    @if(isset($b2cConfigs) && $b2cConfigs->count() > 0)
    <div class="card mt-2">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Existing Configuration Details</h5>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshFormToSubmitMode()">
                <i class="fas fa-refresh" style="margin-right: 10px;"></i> Refresh Form
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="configDetailsTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Domain</th>
                            <th>Site Name</th>
                            <th>Platform Name</th>
                            <th>Market Type</th>
                            <th>Portal ID</th>
                            <th>API Keys</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($b2cConfigs as $config)
                        <tr>
                            <td>{{ $config->domain }}</td>
                            <td>{{ $config->site_name }}</td>
                            <td>{{ $config->platform_name }}</td>
                            <td>
                                <span class="badge bg-primary">
                                    {{ strtoupper($config->market_type) }}
                                </span>
                            </td>
                            <td>{{ $config->portal_id }}</td>
                            <td>
                                @php
                                    $apiKeys = \App\Models\ApiKeyList::where('portal_id', $config->portal_id)->get();
                                @endphp
                                <span class="badge bg-primary">{{ $apiKeys->count() }} API Key(s)</span>
                                @if($apiKeys->count() > 0)
                                    <button type="button" class="btn btn-sm btn-primary ms-1" data-bs-toggle="modal" data-bs-target="#apiKeysModal-{{ $config->id }}">
                                        View Details
                                    </button>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-primary">
                                    {{ $config->active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="loadDataAndScrollToForm({{ $config->id }})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- API Keys Modals -->
    @foreach($b2cConfigs as $config)
        @php
            $apiKeys = \App\Models\ApiKeyList::where('portal_id', $config->portal_id)->get();
        @endphp
        @if($apiKeys->count() > 0)
        <div class="modal fade" id="apiKeysModal-{{ $config->id }}" tabindex="-1" aria-labelledby="apiKeysModalLabel-{{ $config->id }}" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="apiKeysModalLabel-{{ $config->id }}">API Keys Details - {{ $config->domain }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>API Key</th>
                                        <th>Client ID</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($apiKeys as $index => $apiKey)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <code class="api-key-display">{{ substr($apiKey->api_key, 0, 10) }}...{{ substr($apiKey->api_key, -5) }}</code>
                                            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="toggleApiKey(this, '{{ $apiKey->api_key }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                        <td>{{ $apiKey->client_id }}</td>
                                        <td>{{ $apiKey->created_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    @endforeach
    @endif

    <div class="card p-3 mt-2">
        <div class="row">
            <div class="col-md-12">
                <form action="{{ route('backend.b2c-agency-config.store') }}" method="POST" id="b2cForm">
                    @csrf
                    <input type="hidden" name="group_id" value="{{ $id }}">
                    <input type="hidden" id="editMode" value="false">
                    
                    <!-- Common Fields Section -->
                    <div class="common-fields">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <label for="market_country" class="form-label">Market Country</label>
                                <select name="market_country" id="market_country" class="form-control select2">
                                    @if (isset($agencyProfile) && is_object($agencyProfile))
                                        <option value="US" {{ $agencyProfile->market_country == 'US' ? 'selected' : '' }}>US</option>
                                        <option value="CA" {{ $agencyProfile->market_country == 'CA' ? 'selected' : '' }}>CA</option>
                                    @else
                                        <option value="">Select Market Country</option>
                                        <option value="US">US</option>
                                        <option value="CA">CA</option>
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label for="currency" class="form-label">Currency</label>
                                <select name="currency" id="currency" class="form-control select2">
                                    @if (isset($agencyProfile) && is_object($agencyProfile))
                                        <option value="CAD" {{ $agencyProfile->currency == 'CAD' ? 'selected' : '' }}>CAD</option>
                                        <option value="USD" {{ $agencyProfile->currency == 'USD' ? 'selected' : '' }}>USD</option>
                                    @else
                                        <option value="">Select Currency</option>
                                        <option value="CAD">CAD</option>
                                        <option value="USD">USD</option>
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label">Status</label>
                                <div class="form-check form-switch">
                                    <input type="hidden" name="status" value="0">
                                    <input class="form-check-input" type="checkbox" name="status" id="status" value="1"
                                        @if (isset($agencyProfile) && is_object($agencyProfile))
                                            {{ $agencyProfile->status == 1 ? 'checked' : '' }}
                                        @endif>
                                    <label class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <fieldset class="b2c_config_field">
                        <div id="b2c-container">
                            <div class="config-card b2c-block">
                                <div class="row">
                                    <div class="col-md-3 mb-4">
                                        <label for="domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control" name="b2c_config[0][domain]" id="domain">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_name]" id="site_name">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="platform_name" class="form-label">Platform Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][platform_name]" id="platform_name">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_email" class="form-label">Site Email</label>
                                        <input type="email" class="form-control" name="b2c_config[0][site_email]" id="site_email">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="market_type" class="form-label">Market Type</label>
                                        <select name="b2c_config[0][market_type]" id="market_type" class="form-control select2">
                                            <option value="">Select Market Type</option>
                                            <option value="b2c">B2C</option>
                                            <option value="b2b">B2B</option>
                                            <option value="meta">Meta</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_phone" class="form-label">Site Phone</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_phone]" id="site_phone">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="portal_id" class="form-label">Portal Id</label>
                                        <input type="text" class="form-control @error('portal_id') is-invalid @enderror" name="b2c_config[0][portal_id]" id="portal_id">
                                        @error('portal_id')
                                            <div class="invalid-feedback">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Resource</label>
                                        <input type="text" class="form-control" name="b2c_config[0][rsource]" id="rsource">
                                    </div>
                                    
                                    <!-- API Key and Client ID Section -->
                                    <div class="col-md-12">
                                        <div id="api-client-container-0">
                                            <div class="api-client-card">
                                                <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                <div class="row">
                                                    <div class="col-md-6 mb-2">
                                                        <label class="form-label">API Key</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="" readonly>
                                                            <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                <i class="fas fa-sync-alt"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 mb-2">
                                                        <label class="form-label">Client Id</label>
                                                        <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button type="button" class="btn btn-sm btn-primary" onclick="addApiClientCard(0)">Add API Key/Client</button>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Content Sources</label>
                                        <select class="form-control select2-content-sources" multiple="multiple"
                                            name="b2c_config[0][content_sources][]" id="content_sources">
                                            @foreach($content_sources as $source)
                                                <option value="{{ $source['id'] }}">
                                                    {{ $source['name'] }} ({{ $source['identifier'] }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Landing Url</label>
                                        <input type="text" class="form-control" name="b2c_config[0][landing_url]" id="landing_url">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Booking Mode</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][gds_book]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][gds_book]" id="gds_book" value="1">
                                            <label class="form-check-label">GDS Book</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Active</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][active]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][active]" id="active" value="1">
                                            <label class="form-check-label">Active</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </fieldset>

                    <div class="row mt-4">
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary" id="submitBtn">Submit</button>
                        </div>
                        <div class="col-md-6">
                            @if ($errors->any())
                                <div class="alert alert-danger" id="error-alert">
                                    <ul class="mb-0" style="list-style: none">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <div id="error-message"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Initialize all Select2 dropdowns
            $('.select2').select2();

            // Initialize Select2 for content sources
            $('.select2-content-sources').select2({
                placeholder: "Select content sources",
                allowClear: true,
                width: '100%'
            });

            // Initialize DataTable for configuration details
            @if(isset($b2cConfig) && $b2cConfig)
            $('#configDetailsTable').DataTable({
                responsive: true,
                pageLength: 10,
                lengthChange: false,
                searching: false,
                ordering: false,
                info: false,
                paging: false,
                dom: 't'
            });
            @endif

            // Auto-generate API key for empty fields on page load
            $('.api-key-field').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Auto-generate API key for any empty API key fields
            $('input[name$="[api_key]"]').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Close alert after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);

            // Check for portal ID error and show alert
            @if($errors->has('portal_id'))
                Swal.fire({
                    icon: 'error',
                    title: 'Duplicate Portal ID',
                    text: '{{ $errors->first('portal_id') }}',
                    confirmButtonText: 'OK'
                });
            @endif
        });

        // Generate random API key
        function generateRandomApiKey(length = 40) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let apiKey = '';
            for (let i = 0; i < length; i++) {
                apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return apiKey;
        }

        // Generate API key for specific field
        function generateApiKey(button) {
            const inputField = $(button).closest('.input-group').find('input');
            inputField.val(generateRandomApiKey());
        }

        // Add new API Key/Client ID card
        function addApiClientCard(index) {
            const container = $(`#api-client-container-${index}`);
            const cardCount = container.children('.api-client-card').length;

            const newCard = `
                <div class="api-client-card">
                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="text" class="form-control api-key-field" name="b2c_config[${index}][api_keys][${cardCount}][api_key]" value="${generateRandomApiKey()}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">Client Id</label>
                            <input type="text" class="form-control" name="b2c_config[${index}][api_keys][${cardCount}][client_id]">
                        </div>
                    </div>
                </div>
            `;

            container.append(newCard);
        }

        // Remove API Key/Client ID card
        function removeApiClientCard(element) {
            const container = $(element).closest('[id^="api-client-container-"]');
            if (container.children().length > 1) {
                $(element).closest('.api-client-card').remove();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Cannot remove',
                    text: 'You must have at least one API Key/Client ID card',
                });
            }
        }

        // Load data and scroll to form function
        function loadDataAndScrollToForm(configId) {
            // Set edit mode
            $('#editMode').val('true');

            // Change form action to update
            $('#b2cForm').attr('action', '{{ route("b2c.update") }}');

            // Update field names for edit mode
            updateFieldNamesForEdit();

            // Load existing B2C configuration data into form
            if (configId) {
                loadB2CConfigData(configId);
            }

            // Change submit button text and color
            $('#submitBtn').text('Update').removeClass('btn-success').addClass('btn-primary');

            // Scroll to form
            $('html, body').animate({
                scrollTop: $("#b2cForm").offset().top - 100
            }, 800);
        }

        // Load existing B2C configuration data into form (only when Edit is clicked)
        function loadB2CConfigData(configId) {
            @if(isset($b2cConfigs) && $b2cConfigs->count() > 0)
                // Find the specific configuration
                var configs = @json($b2cConfigs);
                var selectedConfig = configs.find(config => config.id == configId);

                if (selectedConfig) {
                    // Load B2C Config data
                    $('#domain').val(selectedConfig.domain);
                    $('#site_name').val(selectedConfig.site_name);
                    $('#platform_name').val(selectedConfig.platform_name);
                    $('#site_email').val(selectedConfig.site_email);
                    $('#market_type').val(selectedConfig.market_type).trigger('change');
                    $('#site_phone').val(selectedConfig.site_phone);
                    $('#portal_id').val(selectedConfig.portal_id);
                    $('#rsource').val(selectedConfig.rsource);
                    $('#landing_url').val(selectedConfig.landing_url);
                    $('#gds_book').prop('checked', selectedConfig.booking_mode == 1);
                    $('#active').prop('checked', selectedConfig.active == 1);

                    // Load Content Sources
                    if (selectedConfig.content_sources) {
                        var selectedSources = selectedConfig.content_sources.split(',');
                        $('#content_sources').val(selectedSources).trigger('change');
                    }

                    // Load API Keys for this specific configuration
                    loadApiKeys(selectedConfig.portal_id);
                }
            @endif
        }

        // Load API keys into form
        function loadApiKeys(portalId) {
            // Clear existing API key cards
            $('#api-client-container-0').empty();

            if (portalId) {
                // Make AJAX call to get API keys for this portal
                $.ajax({
                    url: '/admin/get-api-keys/' + portalId,
                    method: 'GET',
                    success: function(response) {
                        if (response.apiKeys && response.apiKeys.length > 0) {
                            response.apiKeys.forEach(function(apiKey, index) {
                                var apiKeyCard = `
                                    <div class="api-client-card">
                                        <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <label class="form-label">API Key</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][${index}][api_key]" value="${apiKey.api_key}" readonly>
                                                    <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                        <i class="fas fa-sync-alt"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-6 mb-2">
                                                <label class="form-label">Client Id</label>
                                                <input type="text" class="form-control" name="b2c_config[0][api_keys][${index}][client_id]" value="${apiKey.client_id}">
                                            </div>
                                        </div>
                                    </div>
                                `;
                                $('#api-client-container-0').append(apiKeyCard);
                            });
                        } else {
                            addDefaultApiKeyCard();
                        }
                    },
                    error: function() {
                        addDefaultApiKeyCard();
                    }
                });
            } else {
                addDefaultApiKeyCard();
            }
        }



        // Add default API key card
        function addDefaultApiKeyCard() {
            var defaultCard = `
                <div class="api-client-card">
                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="${generateRandomApiKey()}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">Client Id</label>
                            <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                        </div>
                    </div>
                </div>
            `;
            $('#api-client-container-0').append(defaultCard);
        }

        // Update field names for edit mode
        function updateFieldNamesForEdit() {
            $('#market_country').attr('name', 'edit_market_country');
            $('#currency').attr('name', 'edit_currency');
            $('input[name="status"]').attr('name', 'edit_status');
        }

        // Refresh form to submit mode
        function refreshFormToSubmitMode() {
            // Reset edit mode
            $('#editMode').val('false');

            // Change form action back to store
            $('#b2cForm').attr('action', '{{ route("backend.b2c-agency-config.store") }}');

            // Reset field names to create mode
            resetFieldNamesToCreateMode();

            // Change submit button text back to Submit
            $('#submitBtn').text('Submit').removeClass('btn-success').addClass('btn-primary');

            // Clear all B2C configuration fields (keep common fields)
            clearB2CConfigFields();

            // Reset API keys to default
            resetApiKeysToDefault();

            // Scroll to form
            $('html, body').animate({
                scrollTop: $("#b2cForm").offset().top - 100
            }, 800);
        }

        // Reset field names to create mode
        function resetFieldNamesToCreateMode() {
            $('#market_country').attr('name', 'market_country');
            $('#currency').attr('name', 'currency');
            $('input[name="edit_status"]').attr('name', 'status');
        }

        // Clear B2C configuration fields
        function clearB2CConfigFields() {
            // Clear text inputs
            $('#domain').val('');
            $('#site_name').val('');
            $('#platform_name').val('');
            $('#site_email').val('');
            $('#site_phone').val('');
            $('#portal_id').val('');
            $('#rsource').val('');
            $('#landing_url').val('');

            // Reset select dropdowns
            $('#market_type').val('').trigger('change');
            $('#content_sources').val(null).trigger('change');

            // Reset checkboxes
            $('#gds_book').prop('checked', false);
            $('#active').prop('checked', false);
        }

        // Reset API keys to default
        function resetApiKeysToDefault() {
            // Clear existing API key cards
            $('#api-client-container-0').empty();

            // Add default empty API key card
            addDefaultApiKeyCard();
        }

        // Toggle API key visibility
        function toggleApiKey(button, fullApiKey) {
            const codeElement = $(button).siblings('.api-key-display');
            const icon = $(button).find('i');

            if (icon.hasClass('fa-eye')) {
                // Show full API key
                codeElement.text(fullApiKey);
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                // Hide API key
                const maskedKey = fullApiKey.substr(0, 10) + '...' + fullApiKey.substr(-5);
                codeElement.text(maskedKey);
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        }




    </script>
@endsection