@extends('layouts/layoutMaster')
@section('title', 'B2C Config')
@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/katex.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/quill/editor.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('assets/vendor/js/dropdown-hover.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/katex.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/quill/quill.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/agencygroup/index.js') }}"></script>
@endsection

@section('content')
    <style>
        .b2c_config_field {
            position: relative;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .b2c_config_field::before {
            position: absolute;
            content: "Configuration";
            top: -12px;
            left: 20px;
            background-color: white;
            padding: 0 10px;
            font-weight: bold;
            color: #333;
        }
        
        .config-card {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            position: relative;
            background-color: #f9f9f9;
        }
        

        
        .common-fields {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #eee;
        }
        
        .select2-container {
            width: 100% !important;
        }
        
        .header {
            background-color: #7367f0;
        }
        
        .header h5 {
            color: white;
        }
        
        #b2c-container {
            margin-top: 20px;
        }
        

        
        .api-client-card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            position: relative;
        }
        
        .remove-api-client {
            position: absolute;
            top: 5px;
            right: 5px;
            color: #dc3545;
            cursor: pointer;
        }
    </style>

    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex">
                <h5 class="mx-2 mt-2 text-white">B2C Config</h5>
            </div>
        </div>
    </div>
    
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show mt-2" role="alert" id="success-alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="card p-3 mt-2">
        <div class="row">
            <div class="col-md-12">
                <form action="{{ isset($b2cConfig) ? route('b2c.update') : route('backend.b2c-agency-config.store') }}" method="POST" id="b2cForm">
                    @csrf
                    <input type="hidden" name="group_id" value="{{ $id }}">
                    
                    <!-- Common Fields Section -->
                    <div class="common-fields">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <label for="market_country" class="form-label">Market Country</label>
                                @if (isset($agencyProfile) && is_object($agencyProfile))
                                    <select name="edit_market_country" class="form-control select2">
                                        <option value="US" {{ $agencyProfile->market_country == 'US' ? 'selected' : '' }}>US</option>
                                        <option value="CA" {{ $agencyProfile->market_country == 'CA' ? 'selected' : '' }}>CA</option>
                                    </select>
                                @else
                                    <select name="market_country" class="form-control select2">
                                        <option value="US" selected>US</option>
                                        <option value="CA">CA</option>
                                    </select>
                                @endif
                            </div>
                            <div class="col-md-4 mb-4">
                                <label for="currency" class="form-label">Currency</label>
                                @if (isset($agencyProfile) && is_object($agencyProfile))
                                    <select name="edit_currency" class="form-control select2">
                                        <option value="CAD" {{ $agencyProfile->currency == 'CAD' ? 'selected' : '' }}>CAD</option>
                                        <option value="USD" {{ $agencyProfile->currency == 'USD' ? 'selected' : '' }}>USD</option>
                                    </select>
                                @else
                                    <select name="currency" class="form-control select2">
                                        <option value="CAD">CAD</option>
                                        <option value="USD" selected>USD</option>
                                    </select>
                                @endif
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label">Status</label>
                                <div class="form-check form-switch">
                                    @if (isset($agencyProfile) && is_object($agencyProfile))
                                        <input type="hidden" name="edit_status" value="0">
                                        <input class="form-check-input" type="checkbox" name="edit_status" value="1"
                                            {{ $agencyProfile->status == 1 ? 'checked' : '' }}>
                                    @else
                                        <input type="hidden" name="status" value="0">
                                        <input class="form-check-input" type="checkbox" name="status" value="1">
                                    @endif
                                    <label class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <fieldset class="b2c_config_field">
                        <div id="b2c-container">
                            <div class="config-card b2c-block">
                                <div class="row">
                                    <div class="col-md-3 mb-4">
                                        <label for="domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control" name="b2c_config[0][domain]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->domain : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_name]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->site_name : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="platform_name" class="form-label">Platform Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][platform_name]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->platform_name : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_email" class="form-label">Site Email</label>
                                        <input type="email" class="form-control" name="b2c_config[0][site_email]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->site_email : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_email" class="form-label">Market Type</label>
                                        <select name="b2c_config[0][market_type]" class="form-control select2">
                                            <option value="b2c" {{ isset($b2cConfig) && $b2cConfig->market_type == 'b2c' ? 'selected' : '' }}>B2C</option>
                                            <option value="b2b" {{ isset($b2cConfig) && $b2cConfig->market_type == 'b2b' ? 'selected' : '' }}>B2B</option>
                                            <option value="meta" {{ isset($b2cConfig) && $b2cConfig->market_type == 'meta' ? 'selected' : '' }}>Meta</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_phone" class="form-label">Site Phone</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_phone]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->site_phone : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="portal_id" class="form-label">Portal Id</label>
                                        <input type="text" class="form-control" name="b2c_config[0][portal_id]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->portal_id : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Resource</label>
                                        <input type="text" class="form-control" name="b2c_config[0][rsource]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->rsource : '' }}">
                                    </div>
                                    
                                    <!-- API Key and Client ID Section -->
                                    <div class="col-md-12">
                                        <div id="api-client-container-0">
                                            @if(isset($b2cConfig) && $b2cConfig->portal_id)
                                                @php
                                                    $apiKeys = \App\Models\ApiKeyList::where('portal_id', $b2cConfig->portal_id)->get();
                                                @endphp
                                                @if($apiKeys->count() > 0)
                                                    @foreach($apiKeys as $index => $apiKey)
                                                        <div class="api-client-card">
                                                            <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                            <div class="row">
                                                                <div class="col-md-6 mb-2">
                                                                    <label class="form-label">API Key</label>
                                                                    <div class="input-group">
                                                                        <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][{{ $index }}][api_key]" value="{{ $apiKey->api_key }}" readonly>
                                                                        <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                            <i class="fas fa-sync-alt"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 mb-2">
                                                                    <label class="form-label">Client Id</label>
                                                                    <input type="text" class="form-control" name="b2c_config[0][api_keys][{{ $index }}][client_id]" value="{{ $apiKey->client_id }}">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="api-client-card">
                                                        <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                        <div class="row">
                                                            <div class="col-md-6 mb-2">
                                                                <label class="form-label">API Key</label>
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="" readonly>
                                                                    <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                        <i class="fas fa-sync-alt"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 mb-2">
                                                                <label class="form-label">Client Id</label>
                                                                <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @else
                                                <div class="api-client-card">
                                                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                    <div class="row">
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">API Key</label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="" readonly>
                                                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                    <i class="fas fa-sync-alt"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">Client Id</label>
                                                            <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button type="button" class="btn btn-sm btn-primary" onclick="addApiClientCard(0)">Add API Key/Client</button>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Content Sources</label>
                                        <select class="form-control select2-content-sources" multiple="multiple"
                                            name="b2c_config[0][content_sources][]">
                                            @foreach($content_sources as $source)
                                                @php
                                                    $selectedSources = isset($b2cConfig) && $b2cConfig->content_sources ? explode(',', $b2cConfig->content_sources) : [];
                                                @endphp
                                                <option value="{{ $source['id'] }}" {{ in_array($source['id'], $selectedSources) ? 'selected' : '' }}>
                                                    {{ $source['name'] }} ({{ $source['identifier'] }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Landing Url</label>
                                        <input type="text" class="form-control" name="b2c_config[0][landing_url]"
                                            value="{{ isset($b2cConfig) ? $b2cConfig->landing_url : '' }}">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Booking Mode</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][gds_book]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][gds_book]" value="1"
                                                {{ isset($b2cConfig) && $b2cConfig->booking_mode ? 'checked' : '' }}>
                                            <label class="form-check-label">GDS Book</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Active</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][active]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][active]" value="1"
                                                {{ isset($b2cConfig) && $b2cConfig->active ? 'checked' : '' }}>
                                            <label class="form-check-label">Active</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </fieldset>

                    <div class="row mt-4">
                        <div class="col-md-2">
                            @if (isset($b2cConfig) && is_object($b2cConfig))
                                <button type="submit" class="btn btn-success" id="update">Update</button>
                            @else
                                <button type="submit" class="btn btn-success">Submit</button>
                            @endif
                        </div>
                        <div class="col-md-6">
                            @if ($errors->any())
                                <div class="alert alert-danger" id="error-alert">
                                    <ul class="mb-0" style="list-style: none">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                            <div id="error-message"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Initialize all Select2 dropdowns
            $('.select2').select2();
            
            // Initialize Select2 for content sources
            $('.select2-content-sources').select2({
                placeholder: "Select content sources",
                allowClear: true,
                width: '100%'
            });

            // Auto-generate API key for empty fields on page load
            $('.api-key-field').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Auto-generate API key for any empty API key fields
            $('input[name$="[api_key]"]').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Close alert after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });

        // Generate random API key
        function generateRandomApiKey(length = 40) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let apiKey = '';
            for (let i = 0; i < length; i++) {
                apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return apiKey;
        }

        // Generate API key for specific field
        function generateApiKey(button) {
            const inputField = $(button).closest('.input-group').find('input');
            inputField.val(generateRandomApiKey());
        }

        // Add new API Key/Client ID card
        function addApiClientCard(index) {
            const container = $(`#api-client-container-${index}`);
            const cardCount = container.children('.api-client-card').length;

            const newCard = `
                <div class="api-client-card">
                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="text" class="form-control api-key-field" name="b2c_config[${index}][api_keys][${cardCount}][api_key]" value="${generateRandomApiKey()}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">Client Id</label>
                            <input type="text" class="form-control" name="b2c_config[${index}][api_keys][${cardCount}][client_id]">
                        </div>
                    </div>
                </div>
            `;

            container.append(newCard);
        }

        // Remove API Key/Client ID card
        function removeApiClientCard(element) {
            const container = $(element).closest('[id^="api-client-container-"]');
            if (container.children().length > 1) {
                $(element).closest('.api-client-card').remove();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Cannot remove',
                    text: 'You must have at least one API Key/Client ID card',
                });
            }
        }




    </script>
@endsection