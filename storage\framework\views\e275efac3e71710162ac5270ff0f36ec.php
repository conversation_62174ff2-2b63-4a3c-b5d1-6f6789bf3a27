<?php $__env->startSection('title', 'B2C Config'); ?>
<?php $__env->startSection('vendor-style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave-phone.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/js/dropdown-hover.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/cleavejs/cleave-phone.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
    <script src="<?php echo e(asset('assets/js/agencygroup/index.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <style>
        .b2c_config_field {
            position: relative;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .b2c_config_field::before {
            position: absolute;
            content: "Configuration";
            top: -12px;
            left: 20px;
            background-color: white;
            padding: 0 10px;
            font-weight: bold;
            color: #333;
        }
        
        .config-card {
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            position: relative;
            background-color: #f9f9f9;
        }
        

        
        .common-fields {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #eee;
        }
        
        .select2-container {
            width: 100% !important;
        }
        
        .header {
            background-color: #7367f0;
        }
        
        .header h5 {
            color: white;
        }
        
        #b2c-container {
            margin-top: 20px;
        }
        

        
        .api-client-card {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            position: relative;
        }
        
        .remove-api-client {
            position: absolute;
            top: 5px;
            right: 5px;
            color: #dc3545;
            cursor: pointer;
        }
    </style>

    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex">
                <h5 class="mx-2 mt-2 text-white">B2C Config</h5>
            </div>
        </div>
    </div>
    
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-2" role="alert" id="success-alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Configuration Details Table -->
    <?php if(isset($b2cConfig) && $b2cConfig): ?>
    <div class="card mt-2">
        <div class="card-header">
            <h5 class="card-title mb-0">Existing Configuration Details</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="configDetailsTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Domain</th>
                            <th>Site Name</th>
                            <th>Platform Name</th>
                            <th>Market Type</th>
                            <th>Portal ID</th>
                            <th>API Keys</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php echo e($b2cConfig->domain); ?></td>
                            <td><?php echo e($b2cConfig->site_name); ?></td>
                            <td><?php echo e($b2cConfig->platform_name); ?></td>
                            <td>
                                <span class="badge bg-<?php echo e($b2cConfig->market_type == 'b2c' ? 'primary' : ($b2cConfig->market_type == 'b2b' ? 'success' : 'info')); ?>">
                                    <?php echo e(strtoupper($b2cConfig->market_type)); ?>

                                </span>
                            </td>
                            <td><?php echo e($b2cConfig->portal_id); ?></td>
                            <td>
                                <?php
                                    $apiKeys = \App\Models\ApiKeyList::where('portal_id', $b2cConfig->portal_id)->get();
                                ?>
                                <span class="badge bg-secondary"><?php echo e($apiKeys->count()); ?> API Key(s)</span>
                                <?php if($apiKeys->count() > 0): ?>
                                    <button type="button" class="btn btn-sm btn-outline-info ms-1" data-bs-toggle="modal" data-bs-target="#apiKeysModal">
                                        View Details
                                    </button>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($b2cConfig->active ? 'success' : 'danger'); ?>">
                                    <?php echo e($b2cConfig->active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="scrollToForm()">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- API Keys Modal -->
    <div class="modal fade" id="apiKeysModal" tabindex="-1" aria-labelledby="apiKeysModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="apiKeysModalLabel">API Keys Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>API Key</th>
                                    <th>Client ID</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $apiKeys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $apiKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td>
                                        <code class="api-key-display"><?php echo e(substr($apiKey->api_key, 0, 10)); ?>...<?php echo e(substr($apiKey->api_key, -5)); ?></code>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="toggleApiKey(this, '<?php echo e($apiKey->api_key); ?>')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                    <td><?php echo e($apiKey->client_id); ?></td>
                                    <td><?php echo e($apiKey->created_at->format('M d, Y H:i')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card p-3 mt-2">
        <div class="row">
            <div class="col-md-12">
                <form action="<?php echo e(isset($b2cConfig) ? route('b2c.update') : route('backend.b2c-agency-config.store')); ?>" method="POST" id="b2cForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="group_id" value="<?php echo e($id); ?>">
                    
                    <!-- Common Fields Section -->
                    <div class="common-fields">
                        <div class="row">
                            <div class="col-md-4 mb-4">
                                <label for="market_country" class="form-label">Market Country</label>
                                <?php if(isset($agencyProfile) && is_object($agencyProfile)): ?>
                                    <select name="edit_market_country" class="form-control select2">
                                        <option value="US" <?php echo e($agencyProfile->market_country == 'US' ? 'selected' : ''); ?>>US</option>
                                        <option value="CA" <?php echo e($agencyProfile->market_country == 'CA' ? 'selected' : ''); ?>>CA</option>
                                    </select>
                                <?php else: ?>
                                    <select name="market_country" class="form-control select2">
                                        <option value="US" selected>US</option>
                                        <option value="CA">CA</option>
                                    </select>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label for="currency" class="form-label">Currency</label>
                                <?php if(isset($agencyProfile) && is_object($agencyProfile)): ?>
                                    <select name="edit_currency" class="form-control select2">
                                        <option value="CAD" <?php echo e($agencyProfile->currency == 'CAD' ? 'selected' : ''); ?>>CAD</option>
                                        <option value="USD" <?php echo e($agencyProfile->currency == 'USD' ? 'selected' : ''); ?>>USD</option>
                                    </select>
                                <?php else: ?>
                                    <select name="currency" class="form-control select2">
                                        <option value="CAD">CAD</option>
                                        <option value="USD" selected>USD</option>
                                    </select>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 mb-4">
                                <label class="form-label">Status</label>
                                <div class="form-check form-switch">
                                    <?php if(isset($agencyProfile) && is_object($agencyProfile)): ?>
                                        <input type="hidden" name="edit_status" value="0">
                                        <input class="form-check-input" type="checkbox" name="edit_status" value="1"
                                            <?php echo e($agencyProfile->status == 1 ? 'checked' : ''); ?>>
                                    <?php else: ?>
                                        <input type="hidden" name="status" value="0">
                                        <input class="form-check-input" type="checkbox" name="status" value="1">
                                    <?php endif; ?>
                                    <label class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <fieldset class="b2c_config_field">
                        <div id="b2c-container">
                            <div class="config-card b2c-block">
                                <div class="row">
                                    <div class="col-md-3 mb-4">
                                        <label for="domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control" name="b2c_config[0][domain]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->domain : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_name" class="form-label">Site Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_name]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->site_name : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="platform_name" class="form-label">Platform Name</label>
                                        <input type="text" class="form-control" name="b2c_config[0][platform_name]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->platform_name : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_email" class="form-label">Site Email</label>
                                        <input type="email" class="form-control" name="b2c_config[0][site_email]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->site_email : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_email" class="form-label">Market Type</label>
                                        <select name="b2c_config[0][market_type]" class="form-control select2">
                                            <option value="b2c" <?php echo e(isset($b2cConfig) && $b2cConfig->market_type == 'b2c' ? 'selected' : ''); ?>>B2C</option>
                                            <option value="b2b" <?php echo e(isset($b2cConfig) && $b2cConfig->market_type == 'b2b' ? 'selected' : ''); ?>>B2B</option>
                                            <option value="meta" <?php echo e(isset($b2cConfig) && $b2cConfig->market_type == 'meta' ? 'selected' : ''); ?>>Meta</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="site_phone" class="form-label">Site Phone</label>
                                        <input type="text" class="form-control" name="b2c_config[0][site_phone]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->site_phone : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-4">
                                        <label for="portal_id" class="form-label">Portal Id</label>
                                        <input type="text" class="form-control" name="b2c_config[0][portal_id]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->portal_id : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Resource</label>
                                        <input type="text" class="form-control" name="b2c_config[0][rsource]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->rsource : ''); ?>">
                                    </div>
                                    
                                    <!-- API Key and Client ID Section -->
                                    <div class="col-md-12">
                                        <div id="api-client-container-0">
                                            <?php if(isset($b2cConfig) && $b2cConfig->portal_id): ?>
                                                <?php
                                                    $apiKeys = \App\Models\ApiKeyList::where('portal_id', $b2cConfig->portal_id)->get();
                                                ?>
                                                <?php if($apiKeys->count() > 0): ?>
                                                    <?php $__currentLoopData = $apiKeys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $apiKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="api-client-card">
                                                            <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                            <div class="row">
                                                                <div class="col-md-6 mb-2">
                                                                    <label class="form-label">API Key</label>
                                                                    <div class="input-group">
                                                                        <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][<?php echo e($index); ?>][api_key]" value="<?php echo e($apiKey->api_key); ?>" readonly>
                                                                        <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                            <i class="fas fa-sync-alt"></i>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6 mb-2">
                                                                    <label class="form-label">Client Id</label>
                                                                    <input type="text" class="form-control" name="b2c_config[0][api_keys][<?php echo e($index); ?>][client_id]" value="<?php echo e($apiKey->client_id); ?>">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <div class="api-client-card">
                                                        <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                        <div class="row">
                                                            <div class="col-md-6 mb-2">
                                                                <label class="form-label">API Key</label>
                                                                <div class="input-group">
                                                                    <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="" readonly>
                                                                    <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                        <i class="fas fa-sync-alt"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 mb-2">
                                                                <label class="form-label">Client Id</label>
                                                                <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <div class="api-client-card">
                                                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                                                    <div class="row">
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">API Key</label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control api-key-field" name="b2c_config[0][api_keys][0][api_key]" value="" readonly>
                                                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                                                    <i class="fas fa-sync-alt"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6 mb-2">
                                                            <label class="form-label">Client Id</label>
                                                            <input type="text" class="form-control" name="b2c_config[0][api_keys][0][client_id]">
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button type="button" class="btn btn-sm btn-primary" onclick="addApiClientCard(0)">Add API Key/Client</button>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Content Sources</label>
                                        <select class="form-control select2-content-sources" multiple="multiple"
                                            name="b2c_config[0][content_sources][]">
                                            <?php $__currentLoopData = $content_sources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $source): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $selectedSources = isset($b2cConfig) && $b2cConfig->content_sources ? explode(',', $b2cConfig->content_sources) : [];
                                                ?>
                                                <option value="<?php echo e($source['id']); ?>" <?php echo e(in_array($source['id'], $selectedSources) ? 'selected' : ''); ?>>
                                                    <?php echo e($source['name']); ?> (<?php echo e($source['identifier']); ?>)
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Landing Url</label>
                                        <input type="text" class="form-control" name="b2c_config[0][landing_url]"
                                            value="<?php echo e(isset($b2cConfig) ? $b2cConfig->landing_url : ''); ?>">
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Booking Mode</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][gds_book]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][gds_book]" value="1"
                                                <?php echo e(isset($b2cConfig) && $b2cConfig->booking_mode ? 'checked' : ''); ?>>
                                            <label class="form-check-label">GDS Book</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <label class="form-label">Active</label>
                                        <div class="form-check form-switch">
                                            <input type="hidden" name="b2c_config[0][active]" value="0">
                                            <input class="form-check-input" type="checkbox" name="b2c_config[0][active]" value="1"
                                                <?php echo e(isset($b2cConfig) && $b2cConfig->active ? 'checked' : ''); ?>>
                                            <label class="form-check-label">Active</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </fieldset>

                    <div class="row mt-4">
                        <div class="col-md-2">
                            <?php if(isset($b2cConfig) && is_object($b2cConfig)): ?>
                                <button type="submit" class="btn btn-success" id="update">Update</button>
                            <?php else: ?>
                                <button type="submit" class="btn btn-success">Submit</button>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <?php if($errors->any()): ?>
                                <div class="alert alert-danger" id="error-alert">
                                    <ul class="mb-0" style="list-style: none">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($error); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            <div id="error-message"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Initialize all Select2 dropdowns
            $('.select2').select2();

            // Initialize Select2 for content sources
            $('.select2-content-sources').select2({
                placeholder: "Select content sources",
                allowClear: true,
                width: '100%'
            });

            // Initialize DataTable for configuration details
            <?php if(isset($b2cConfig) && $b2cConfig): ?>
            $('#configDetailsTable').DataTable({
                responsive: true,
                pageLength: 10,
                lengthChange: false,
                searching: false,
                ordering: false,
                info: false,
                paging: false,
                dom: 't'
            });
            <?php endif; ?>

            // Auto-generate API key for empty fields on page load
            $('.api-key-field').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Auto-generate API key for any empty API key fields
            $('input[name$="[api_key]"]').each(function() {
                if (!$(this).val()) {
                    $(this).val(generateRandomApiKey());
                }
            });

            // Close alert after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });

        // Generate random API key
        function generateRandomApiKey(length = 40) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let apiKey = '';
            for (let i = 0; i < length; i++) {
                apiKey += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return apiKey;
        }

        // Generate API key for specific field
        function generateApiKey(button) {
            const inputField = $(button).closest('.input-group').find('input');
            inputField.val(generateRandomApiKey());
        }

        // Add new API Key/Client ID card
        function addApiClientCard(index) {
            const container = $(`#api-client-container-${index}`);
            const cardCount = container.children('.api-client-card').length;

            const newCard = `
                <div class="api-client-card">
                    <span class="remove-api-client" onclick="removeApiClientCard(this)">×</span>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <label class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="text" class="form-control api-key-field" name="b2c_config[${index}][api_keys][${cardCount}][api_key]" value="${generateRandomApiKey()}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="generateApiKey(this)">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <label class="form-label">Client Id</label>
                            <input type="text" class="form-control" name="b2c_config[${index}][api_keys][${cardCount}][client_id]">
                        </div>
                    </div>
                </div>
            `;

            container.append(newCard);
        }

        // Remove API Key/Client ID card
        function removeApiClientCard(element) {
            const container = $(element).closest('[id^="api-client-container-"]');
            if (container.children().length > 1) {
                $(element).closest('.api-client-card').remove();
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Cannot remove',
                    text: 'You must have at least one API Key/Client ID card',
                });
            }
        }

        // Scroll to form function
        function scrollToForm() {
            $('html, body').animate({
                scrollTop: $("#b2cForm").offset().top - 100
            }, 800);
        }

        // Toggle API key visibility
        function toggleApiKey(button, fullApiKey) {
            const codeElement = $(button).siblings('.api-key-display');
            const icon = $(button).find('i');

            if (icon.hasClass('fa-eye')) {
                // Show full API key
                codeElement.text(fullApiKey);
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                // Hide API key
                const maskedKey = fullApiKey.substr(0, 10) + '...' + fullApiKey.substr(-5);
                codeElement.text(maskedKey);
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        }




    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts/layoutMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/agencygroup/b2cagencyconfig/form.blade.php ENDPATH**/ ?>