<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class B2CAgencyConfig extends Model
{
    use HasFactory;
    protected $table="b2c_agency_configs";
    protected $fillable = [
        'market_country', 'currency','group_id',
        'is_status', 'b2c_config', 'meta_config','b2b_config',
    ];

    protected $casts = [
        'status' => 'boolean',
        'b2c_config' => 'array',
        'meta_config' => 'array',
        'b2b_config' => 'array',
    ];
}
